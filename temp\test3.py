import time
from bs4 import BeautifulSoup
from DrissionPage import Chromium, ChromiumOptions

def parse_articles(html_content):
    """解析文章列表"""
    soup = BeautifulSoup(html_content, "html.parser")

    # 查找包含最新发布文章的容器
    articles_container = soup.find("div", {"class": "row gutters", "id": "latest-published-articles"})

    if not articles_container:
        print("未找到文章容器")
        return []

    # 查找所有文章项
    article_items = articles_container.find_all("div", class_="js-article-item")

    articles = []

    for item in article_items:
        try:
            # 提取标题和链接
            title_link = item.find("a", class_="js-article__item__title__link")
            if title_link:
                title = title_link.find("span", class_="anchor-text")
                title_text = title.get_text(strip=True) if title else "无标题"
                link = title_link.get("href", "")
            else:
                title_text = "无标题"
                link = ""

            # 提取作者信息
            authors_div = item.find("div", class_="js-article__item__authors")
            authors = authors_div.get_text(strip=True) if authors_div else "无作者信息"

            # 提取日期
            date_span = item.find("span", class_="js-article-item-date")
            date = date_span.get_text(strip=True) if date_span else "无日期"

            # 提取文章类型
            article_type_span = item.find("span", class_="js-article-subtype")
            article_type = article_type_span.get_text(strip=True) if article_type_span else "未知类型"

            article_info = {
                "title": title_text,
                "authors": authors,
                "link": link,
                "date": date,
                "type": article_type
            }

            articles.append(article_info)

        except Exception as e:
            print(f"解析文章时出错: {e}")
            continue

    return articles

if __name__ == '__main__':
    # 创建浏览器实例
    co = ChromiumOptions()
    # co.headless()  # 取消注释以启用无头模式
    browser = Chromium(co)
    tab = browser.latest_tab

    try:
        # 访问目标网页
        print("正在访问网页...")
        start_time = time.time()
        tab.get('https://www.sciencedirect.com/journal/chemosphere')
        print(f"页面加载耗时: {time.time() - start_time:.2f}秒")

        # 等待页面完全加载
        time.sleep(3)

        # 获取页面HTML内容
        html_content = tab.html

        # 解析文章列表
        print("\n开始解析文章列表...")
        articles = parse_articles(html_content)

        # 输出结果
        print(f"\n共找到 {len(articles)} 篇文章:")
        print("-" * 80)

        for i, article in enumerate(articles, 1):
            print(f"\n文章 {i}:")
            print(f"标题: {article['title']}")
            print(f"作者: {article['authors']}")
            print(f"链接: {article['link']}")
            print(f"日期: {article['date']}")
            print(f"类型: {article['type']}")
            print("-" * 80)

    except Exception as e:
        print(f"程序执行出错: {e}")

    finally:
        # 关闭浏览器
        browser.quit()
        print("\n浏览器已关闭")