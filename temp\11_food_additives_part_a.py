#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Taylor & Francis Food Additives期刊文章抓取脚本
使用Selenium抓取https://www.tandfonline.com/action/showAxaArticles?journalCode=tfac20页面的文章数据
"""

import re
import time
import http.client
import urllib.parse
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup


class ScrapingConfig:
    """抓取配置类"""
    # 摘要获取配置
    ABSTRACT_MAX_RETRIES = 3  # 最大重试次数
    ABSTRACT_TIMEOUT = 30     # 请求超时时间（秒）
    ABSTRACT_SUCCESS_DELAY = 5  # 成功获取后的等待时间（秒）
    ABSTRACT_FAILURE_DELAY = 8  # 失败后的等待时间（秒）
    ABSTRACT_RETRY_BASE_DELAY = 5  # 重试基础延迟时间（秒）

    # 页面抓取配置
    PAGE_LOAD_WAIT = 5  # 页面加载等待时间（秒）


class TandFArticleScraper:
    def __init__(self):
        self.base_url = "https://www.tandfonline.com/action/showAxaArticles?journalCode=tfac20"
        self.driver = None
        self.setup_driver()

    def setup_driver(self):
        options = Options()
        # options.add_argument('--headless')
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        # options.add_argument('--disable-web-security')
        options.add_argument('--allow-running-insecure-content')
        options.add_argument('--disable-extensions')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")

        # 添加一些额外的选项来避免检测
        prefs = {
            "profile.default_content_setting_values": {
                "notifications": 2
            }
        }
        options.add_experimental_option("prefs", prefs)

        self.driver = webdriver.Chrome(options=options)

        # 执行多个反检测脚本
        self.driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
            "source": """
            Object.defineProperty(navigator, 'webdriver', {
              get: () => undefined
            });
            Object.defineProperty(navigator, 'plugins', {
              get: () => [1, 2, 3, 4, 5]
            });
            Object.defineProperty(navigator, 'languages', {
              get: () => ['en-US', 'en']
            });
            window.chrome = {
              runtime: {}
            };
            """
        })

    def extract_article_data(self, article_element):
        try:
            title_link = article_element.find('div', class_='art_title linkable')
            if not title_link:
                return None

            title_a = title_link.find('a', class_='ref nowrap')
            if not title_a:
                return None

            title_span = title_a.find('span', class_='hlFld-Title')
            if title_span:
                title = title_span.get_text(strip=True)
            else:
                title = title_a.get_text(strip=True)

            article_url = title_a.get('href', '')
            if article_url and not article_url.startswith('http'):
                article_url = f"https://www.tandfonline.com{article_url}"

            authors = []
            authors_div = article_element.find('div', class_='tocAuthors articleEntryAuthor')
            if authors_div:
                author_links = authors_div.find_all('a', class_='entryAuthor linkable hlFld-ContribAuthor')
                for author_link in author_links:
                    author_name = author_link.get_text(strip=True)
                    if author_name:
                        authors.append(author_name)

            authors_str = ', '.join(authors) if authors else ''

            pub_date = ''
            date_div = article_element.find('div', class_='tocEPubDate')
            if date_div:
                date_span = date_div.find('span', class_='date')
                if date_span:
                    pub_date = date_span.get_text(strip=True)

            return {
                'title': title,
                'url': article_url,
                'authors': authors_str,
                'publication_date': pub_date,
                'abstract': ''  # 初始化为空，后续获取
            }

        except Exception as e:
            print(f"提取文章数据时出错: {e}")
            return None

    def get_article_abstract(self, article_url, max_retries=None, timeout=None):
        """根据文章链接获取摘要，支持重试和超时控制"""
        # 使用配置的默认值
        if max_retries is None:
            max_retries = ScrapingConfig.ABSTRACT_MAX_RETRIES
        if timeout is None:
            timeout = ScrapingConfig.ABSTRACT_TIMEOUT

        for attempt in range(max_retries):
            try:
                # 解析URL
                parsed_url = urllib.parse.urlparse(article_url)
                host = parsed_url.netloc
                path = parsed_url.path

                # 建立HTTPS连接，设置超时
                conn = http.client.HTTPSConnection(host, timeout=timeout)

                headers = {
                    'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
                    'Accept': '*/*',
                    'Host': 'www.tandfonline.com',
                    'Connection': 'keep-alive',
                    'Cookie': 'MAID=ljayeMBcOyncAWx1O1Z/4Q==; MACHINE_LAST_SEEN=2025-08-21T02%3A06%3A30.777-07%3A00; JSESSIONID=4465698636444CDCC5B3C7357554243F'
                }

                # 发送GET请求
                conn.request("GET", path, '', headers)

                # 等待响应，设置超时
                res = conn.getresponse()

                # 检查响应状态
                if res.status != 200:
                    conn.close()
                    if attempt < max_retries - 1:
                        wait_time = ScrapingConfig.ABSTRACT_RETRY_BASE_DELAY * (attempt + 1)
                        time.sleep(wait_time)
                        continue
                    else:
                        return f"HTTP错误: {res.status}"

                html = res.read().decode("utf-8")
                conn.close()

                # 解析HTML
                soup = BeautifulSoup(html, "html.parser")

                # 提取abstract内容
                abstract_div = soup.find("div", id="abstractId1")
                if abstract_div:
                    paragraph = abstract_div.find("p", class_="last")
                    if paragraph:
                        abstract_text = paragraph.get_text(strip=True)
                        return abstract_text

                # 如果没有找到摘要，尝试其他选择器
                abstract_section = soup.find("section", class_="abstract")
                if abstract_section:
                    abstract_p = abstract_section.find("p")
                    if abstract_p:
                        abstract_text = abstract_p.get_text(strip=True)
                        return abstract_text

                return "未找到摘要"

            except (http.client.HTTPException, ConnectionError, TimeoutError) as e:
                if attempt < max_retries - 1:
                    wait_time = ScrapingConfig.ABSTRACT_RETRY_BASE_DELAY * 2 * (attempt + 1)  # 网络错误等待更长时间
                    time.sleep(wait_time)
                else:
                    return "网络连接失败"
            except Exception as e:
                if attempt < max_retries - 1:
                    wait_time = ScrapingConfig.ABSTRACT_RETRY_BASE_DELAY * (attempt + 1)
                    time.sleep(wait_time)
                else:
                    return "获取摘要失败"

        return "重试次数已用完"



    def wait_for_page_load(self):
        """等待页面完全加载"""
        try:
            # 等待页面加载完成的多种指标
            WebDriverWait(self.driver, 20).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )

            # 尝试等待文章元素出现
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CLASS_NAME, "articleEntry"))
                )
                print("✓ 文章列表加载成功")
                return True
            except:
                # 如果没有找到文章元素，检查是否有其他内容
                soup = BeautifulSoup(self.driver.page_source, 'html.parser')
                if soup.find('div', class_='tocArticleEntry'):
                    print("✓ 找到替代的文章元素")
                    return True
                else:
                    print("⚠ 未找到预期的文章元素")
                    return False

        except Exception as e:
            print(f"等待页面加载时出错: {e}")
            return False

    def scrape_page(self, url):
        try:
            print(f"正在访问: {url}")
            self.driver.get(url)
            time.sleep(5)

            # 等待页面完全加载
            if not self.wait_for_page_load():
                print(f"页面加载可能有问题")
                print(f"当前页面标题: {self.driver.title}")
                print(f"当前URL: {self.driver.current_url}")

            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            article_entries = soup.find_all('div', class_='articleEntry')

            if not article_entries:
                # 尝试查找其他可能的文章容器
                article_entries = soup.find_all('div', class_='tocArticleEntry')
                if article_entries:
                    print("使用备用选择器找到文章")

            articles = []
            for entry in article_entries:
                article_data = self.extract_article_data(entry)
                if article_data:
                    articles.append(article_data)

            print(f"在页面中找到 {len(articles)} 篇文章")

            # 获取每篇文章的摘要
            print("开始获取文章摘要...")

            for i, article in enumerate(articles, 1):
                print(f"正在获取第 {i}/{len(articles)} 篇文章的摘要...")

                if article['url']:
                    abstract = self.get_article_abstract(article['url'])
                    article['abstract'] = abstract

                    # 根据结果调整等待时间
                    if "失败" in abstract or "错误" in abstract or "重试" in abstract:
                        wait_time = ScrapingConfig.ABSTRACT_FAILURE_DELAY
                    else:
                        wait_time = ScrapingConfig.ABSTRACT_SUCCESS_DELAY

                    time.sleep(wait_time)
                else:
                    article['abstract'] = "无有效链接"

            return articles

        except Exception as e:
            print(f"抓取页面时出错: {e}")
            return []

    def check_for_pagination(self):
        try:
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            pagination_elements = soup.find_all(['a', 'button'], string=re.compile(r'Next|Previous|\d+'))

            if pagination_elements:
                print(f"检测到可能的分页元素: {len(pagination_elements)} 个")
                return True
            else:
                print("未检测到分页元素")
                return False

        except Exception as e:
            print(f"检查分页时出错: {e}")
            return False

    def scrape_all_articles(self):
        all_articles = []

        try:
            articles = self.scrape_page(self.base_url)
            all_articles.extend(articles)

            print(f"第1页抓取到 {len(articles)} 篇文章")

            has_pagination = self.check_for_pagination()

            if not has_pagination:
                print("未检测到分页，只抓取当前页面")

        except KeyboardInterrupt:
            print(f"\n用户中断，已抓取 {len(all_articles)} 篇文章")
        except Exception as e:
            print(f"抓取过程中出错: {e}")

        return all_articles

    def close(self):
        if self.driver:
            self.driver.quit()


def main():
    print("Taylor & Francis Food Additives期刊文章抓取脚本")
    print("-" * 60)

    scraper = TandFArticleScraper()

    try:
        print("开始抓取文章...")
        articles = scraper.scrape_all_articles()

        print(f"\n" + "=" * 60)
        print(f"抓取完成！总共获取到 {len(articles)} 篇文章")
        print("=" * 60)

        if articles:
            for i, article in enumerate(articles, 1):
                print(f"\n{i}. 标题: {article['title']}")
                print(f"   链接: {article['url']}")
                print(f"   作者: {article['authors']}")
                print(f"   发布日期: {article['publication_date']}")
                print(f"   摘要: {article['abstract']}")
                print("-" * 60)
        else:
            print("未获取到任何文章，可能是:")
            print("1. 网站有反爬虫保护")
            print("2. 页面结构发生变化")
            print("3. 需要手动完成验证")

    finally:
        scraper.close()


if __name__ == "__main__":
    main()