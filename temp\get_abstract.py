import time
from bs4 import BeautifulSoup
from DrissionPage import Chromium, ChromiumOptions

def get_article_abstract(article_url):
    """获取指定文章的摘要"""
    try:
        # 创建浏览器实例
        co = ChromiumOptions()
        # co.headless()  # 可选择启用无头模式
        browser = Chromium(co)
        tab = browser.latest_tab
        
        # 构建完整URL
        if article_url.startswith('/'):
            full_url = f"https://www.sciencedirect.com{article_url}"
        else:
            full_url = article_url
            
        print(f"正在访问文章页面: {full_url}")
        tab.get(full_url)
        
        # 等待页面加载
        time.sleep(5)
        
        # 获取页面HTML
        html_content = tab.html
        soup = BeautifulSoup(html_content, "html.parser")
        
        # 查找摘要容器 - 根据你提供的结构
        abstract_div = soup.find("div", {"class": "abstract author", "id": "abs0010"})
        
        if abstract_div:
            # 查找摘要内容段落
            abstract_para = abstract_div.find("div", id="abspara0010")
            if abstract_para:
                abstract_text = abstract_para.get_text(strip=True)
                browser.quit()
                return abstract_text
            else:
                # 尝试查找其他可能的摘要段落
                abstract_content = abstract_div.find("div", class_="u-margin-s-bottom")
                if abstract_content:
                    abstract_text = abstract_content.get_text(strip=True)
                    browser.quit()
                    return abstract_text
        
        # 如果没找到指定结构，尝试通用摘要查找
        abstract_general = soup.find("div", class_="abstract")
        if abstract_general:
            # 查找段落内容
            paragraphs = abstract_general.find_all("div")
            for para in paragraphs:
                if para.get_text(strip=True) and len(para.get_text(strip=True)) > 50:
                    abstract_text = para.get_text(strip=True)
                    browser.quit()
                    return abstract_text
        
        # 尝试查找任何包含"abstract"的div
        all_abstracts = soup.find_all("div", class_=lambda x: x and "abstract" in x.lower())
        for abstract in all_abstracts:
            text = abstract.get_text(strip=True)
            if len(text) > 100:  # 确保是实际的摘要内容
                browser.quit()
                return text
            
        browser.quit()
        return "未找到摘要内容"
        
    except Exception as e:
        print(f"获取摘要时出错: {e}")
        return f"获取摘要失败: {e}"

if __name__ == '__main__':
    # 测试获取指定文章的摘要
    test_url = "https://www.sciencedirect.com/science/article/pii/S0045653525005946"
    
    print("开始获取文章摘要...")
    abstract = get_article_abstract(test_url)
    
    print("\n文章摘要:")
    print("=" * 80)
    print(abstract)
    print("=" * 80)
